class Webhooks::FacebookEventsJob < MutexApplicationJob
  queue_as :default
  retry_on LockAcquisitionError, wait: 1.second, attempts: 8
  retry_on StandardError, wait: 2.seconds, attempts: 3

  def perform(message)
    Rails.logger.info("Processing Facebook webhook message: #{message}")

    response = ::Integrations::Facebook::MessageParser.new(message)

    # Kiểm tra xem có channel tương ứng không
    channel = Channel::FacebookPage.find_by(page_id: response.recipient_id)
    unless channel
      Rails.logger.warn("No Facebook channel found for page_id: #{response.recipient_id}")
      return
    end

    key = format(::Redis::Alfred::FACEBOOK_MESSAGE_MUTEX, sender_id: response.sender_id, recipient_id: response.recipient_id)
    with_lock(key) do
      process_message(response)
    end
  rescue StandardError => e
    Rails.logger.error("Error processing Facebook webhook: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))
    raise e
  end

  def process_message(response)
    # Xử lý referral events trước khi tạo message
    if response.referral?
      process_referral_event(response)
    else
      ::Integrations::Facebook::MessageCreator.new(response).perform
    end
  rescue StandardError => e
    Rails.logger.error("Error in process_message for sender #{response.sender_id}: #{e.message}")
    # Không re-raise để tránh job retry liên tục cho lỗi không thể khôi phục
  end

  private

  def process_referral_event(response)
    # Tìm inbox từ recipient_id
    inbox = ::Channel::FacebookPage.find_by(page_id: response.recipient_id)&.inbox
    return unless inbox

    # Tìm hoặc tạo contact_inbox
    contact_inbox = ::ContactInboxBuilder.new(
      source_id: response.sender_id,
      inbox: inbox,
      contact_attributes: {
        name: response.sender_name || response.sender_id
      }
    ).perform

    # Xử lý referral data
    Facebook::ReferralProcessorService.new(
      inbox: inbox,
      contact_inbox: contact_inbox,
      params: response.params
    ).perform

    Rails.logger.info("Processed Facebook referral event for sender #{response.sender_id}")
  end
end
