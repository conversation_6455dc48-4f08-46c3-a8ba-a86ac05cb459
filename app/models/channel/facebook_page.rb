# == Schema Information
#
# Table name: channel_facebook_pages
#
#  id                :integer          not null, primary key
#  page_access_token :string           not null
#  user_access_token :string           not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  account_id        :integer          not null
#  instagram_id      :string
#  page_id           :string           not null
#
# Indexes
#
#  index_channel_facebook_pages_on_page_id                 (page_id)
#  index_channel_facebook_pages_on_page_id_and_account_id  (page_id,account_id) UNIQUE
#

class Channel::FacebookPage < ApplicationRecord
  include Channelable
  include Reauthorizable

  self.table_name = 'channel_facebook_pages'

  validates :page_id, uniqueness: { scope: :account_id }

  after_create_commit :subscribe
  before_destroy :unsubscribe

  def name
    'Facebook'
  end

  def create_contact_inbox(instagram_id, name)
    @contact_inbox = ::ContactInboxWithContactBuilder.new({
                                                            source_id: instagram_id,
                                                            inbox: inbox,
                                                            contact_attributes: { name: name }
                                                          }).perform
  end

  def subscribe
    # ref https://developers.facebook.com/docs/messenger-platform/reference/webhook-events
    # Đ<PERSON><PERSON> b<PERSON>o token hợp lệ trước khi subscribe
    refresh_service = Facebook::RefreshOauthTokenService.new(channel: self)
    valid_token = refresh_service.access_token

    Facebook::Messenger::Subscriptions.subscribe(
      access_token: valid_token,
      subscribed_fields: %w[
        messages message_deliveries message_echoes message_reads standby messaging_handovers messaging_postbacks messaging_referrals
      ]
    )
    Rails.logger.info("Successfully subscribed Facebook page #{page_id} to webhook")
    true
  rescue StandardError => e
    Rails.logger.error("Failed to subscribe Facebook page #{page_id}: #{e.message}")
    # Đánh dấu cần reauthorization nếu lỗi liên quan đến token
    if e.message.include?('token') || e.message.include?('permission') || e.message.include?('auth')
      authorization_error! unless reauthorization_required?
    end
    false
  end

  def unsubscribe
    # Sử dụng token hiện tại để unsubscribe, không cần refresh vì đang xóa
    Facebook::Messenger::Subscriptions.unsubscribe(access_token: page_access_token)
    Rails.logger.info("Successfully unsubscribed Facebook page #{page_id} from webhook")
    true
  rescue StandardError => e
    Rails.logger.error("Failed to unsubscribe Facebook page #{page_id}: #{e.message}")
    # Vẫn trả về true vì unsubscribe thất bại không ảnh hưởng đến việc xóa channel
    true
  end

  # Facebook Dataset configuration
  def facebook_dataset_enabled?
    provider_config&.dig('facebook_dataset', 'enabled') == true
  end

  def facebook_dataset_config
    provider_config&.dig('facebook_dataset') || {}
  end

  def update_facebook_dataset_config(config)
    current_config = provider_config || {}
    current_config['facebook_dataset'] = config
    update!(provider_config: current_config)
  end
end
